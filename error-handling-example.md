# نظام معالجة الأخطاء - مثال للاستخدام

## كيفية عمل النظام:

### 1. عند حدوث خطأ من الباك إند:

```json
{
  "error": {
    "errors": {
      "attributes.cityId": ["المدينة مطلوبة"],
      "attributes.unitArea": ["مساحة الوحدة يجب أن تكون أكبر من 0"],
      "specializationScope": ["نطاق التخصص مطلوب"],
      "attributes.roomsCount": ["عدد الغرف مطلوب"],
      "attributes.paymentMethod": ["طريقة الدفع مطلوبة"]
    }
  }
}
```

### 2. النظام يقوم بـ:

1. **تنظيف أسماء الحقول**: إزالة `attributes.` و `[0]` وغيرها
2. **تحديد الخطوة**: معرفة أي خطوة ينتمي إليها كل حقل
3. **ترجمة أسماء الحقول**: تحويل `cityId` إلى "المدينة"
4. **تجميع الأخطاء**: تجميع الأخطاء حسب الخطوة

### 3. النتيجة المعروضة:

```
يرجى تصحيح الأخطاء التالية

📍 Request Settings (خطوة 1)
  • نطاق التخصص: نطاق التخصص مطلوب

📍 Location Information (خطوة 2)  
  • المدينة: المدينة مطلوبة

📍 Unit Information (خطوة 3)
  • مساحة الوحدة: مساحة الوحدة يجب أن تكون أكبر من 0
  • عدد الغرف: عدد الغرف مطلوب

📍 Financial Information (خطوة 5)
  • طريقة الدفع: طريقة الدفع مطلوبة
```

## المميزات:

✅ **تحديد الخطوة**: يوضح أي خطوة بها الخطأ
✅ **ترجمة الحقول**: أسماء عربية واضحة للحقول  
✅ **تجميع منطقي**: الأخطاء مجمعة حسب الخطوة
✅ **تصميم جميل**: واجهة مستخدم واضحة ومنظمة
✅ **سهولة الاستخدام**: المستخدم يعرف بالضبط أين المشكلة

## كيفية الاستخدام:

```typescript
// في حالة الخطأ، النظام يستدعي تلقائياً:
this.handleSubmissionError(err);

// يمكن أيضاً مسح الأخطاء عند بدء التعديل:
this.clearValidationErrors();

// أو الانتقال لخطوة معينة:
this.navigateToErrorStep(2); // للانتقال للخطوة 2
```

## أنواع الأخطاء المدعومة:

1. **أخطاء التحقق من Laravel**: `error.errors`
2. **رسائل خطأ عامة**: `error.message`  
3. **أخطاء غير متوقعة**: رسالة افتراضية

## إضافة حقول جديدة:

لإضافة حقل جديد للنظام، أضف إلى `fieldToStepMap`:

```typescript
'newField': { step: 3, stepName: 'Unit Information' }
```

وأضف الترجمة في `getFieldDisplayName`:

```typescript
'newField': 'اسم الحقل بالعربية'
```
